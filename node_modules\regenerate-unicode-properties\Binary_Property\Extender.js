const set = require('regenerate')(0xB7, 0x640, 0x7FA, 0xA71, 0xAFB, 0xB55, 0xE46, 0xEC6, 0x180A, 0x1843, 0x1AA7, 0x1C36, 0x1C7B, 0x3005, 0xA015, 0xA60C, 0xA9CF, 0xA9E6, 0xAA70, 0xAADD, 0xFF70, 0x10D4E, 0x10D6A, 0x10D6F, 0x11237, 0x1135D, 0x11A98, 0x11DD9, 0x16FE3, 0x1E5EF);
set.addRange(0x2D0, 0x2D1).addRange(0x3031, 0x3035).addRange(0x309D, 0x309E).addRange(0x30FC, 0x30FE).addRange(0xAAF3, 0xAAF4).addRange(0x10781, 0x10782).addRange(0x113D2, 0x113D3).addRange(0x115C6, 0x115C8).addRange(0x16B42, 0x16B43).addRange(0x16FE0, 0x16FE1).addRange(0x16FF2, 0x16FF3).addRange(0x1E13C, 0x1E13D).addRange(0x1E944, 0x1E946);
exports.characters = set;
