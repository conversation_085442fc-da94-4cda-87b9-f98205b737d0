{"name": "mini-css-extract-plugin", "version": "2.9.4", "description": "extracts CSS into separate files", "keywords": ["webpack", "css", "extract", "hmr"], "homepage": "https://github.com/webpack-contrib/mini-css-extract-plugin", "bugs": "https://github.com/webpack-contrib/mini-css-extract-plugin/issues", "repository": "webpack-contrib/mini-css-extract-plugin", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "license": "MIT", "author": "<PERSON> @sokra", "main": "dist/index.js", "types": "types/index.d.ts", "files": ["dist", "types"], "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir types && prettier \"types/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:code": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint:types": "tsc --pretty --noEmit", "lint:es-check": "es-check es5 dist/hmr/hotModuleReplacement.js", "lint": "npm-run-all -l -p \"lint:**\"", "fix:code": "npm run lint:code -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:code fix:prettier", "prepare": "husky install && npm run build", "release": "standard-version", "security": "npm audit --production", "test:only": "cross-env NODE_ENV=test jest", "test:only:experimental": "EXPERIMENTAL_USE_IMPORT_MODULE=true cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "test:manual": "npm run build && webpack serve ./test/manual/src/index.js --open --config ./test/manual/webpack.config.js", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage"}, "dependencies": {"schema-utils": "^4.0.0", "tapable": "^2.2.1"}, "devDependencies": {"@babel/cli": "^7.24.1", "@babel/core": "^7.24.4", "@babel/preset-env": "^7.24.4", "@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@eslint/js": "^9.32.0", "@eslint/markdown": "^7.0.0", "@stylistic/eslint-plugin": "^5.2.2", "@types/node": "^18.15.11", "bootstrap": "^4.6.2", "cross-env": "^7.0.3", "cspell": "^6.31.1", "css-loader": "^6.10.0", "del": "^6.0.0", "del-cli": "^4.0.0", "es-check": "^7.1.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-config-webpack": "^4.4.2", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jsdoc": "^52.0.0", "eslint-plugin-n": "^17.21.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-unicorn": "^60.0.0", "file-loader": "^6.2.0", "globals": "^16.3.0", "husky": "^7.0.0", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "jsdom": "^19.0.0", "lint-staged": "^13.2.1", "memfs": "^3.4.13", "npm-run-all": "^4.1.5", "prettier": "^3.6.0", "prettier-2": "npm:prettier@^2", "sass": "^1.74.1", "sass-loader": "^12.6.0", "standard-version": "^9.3.0", "typescript": "^5.8.0", "typescript-eslint": "^8.38.0", "webpack": "^5.91.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^5.2.1"}, "peerDependencies": {"webpack": "^5.0.0"}, "engines": {"node": ">= 12.13.0"}}