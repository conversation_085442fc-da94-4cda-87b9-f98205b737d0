const set = require('regenerate')(0x305, 0x323, 0x3037, 0x1B000, 0x1B155);
set.addRange(0x3001, 0x3003).addRange(0x3008, 0x3011).addRange(0x3013, 0x301F).addRange(0x3030, 0x3035).addRange(0x303C, 0x303D).addRange(0x3099, 0x309C).addRange(0x30A0, 0x30FF).addRange(0x31F0, 0x31FF).addRange(0x32D0, 0x32FE).addRange(0x3300, 0x3357).addRange(0xFE45, 0xFE46).addRange(0xFF61, 0xFF9F).addRange(0x1AFF0, 0x1AFF3).addRange(0x1AFF5, 0x1AFFB).addRange(0x1AFFD, 0x1AFFE).addRange(0x1B120, 0x1B122).addRange(0x1B164, 0x1B167);
exports.characters = set;
