<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雷达图刻度测试</title>
    <script src="https://unpkg.com/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .title {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        #radarChart {
            width: 100%;
            height: 500px;
        }
        .description {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="title">雷达图刻度显示测试</h2>
        <div id="radarChart"></div>
        <div class="description">
            <strong>功能说明：</strong>
            <ul>
                <li>雷达图显示了5条刻度线（0, 20, 40, 60, 80, 100）</li>
                <li>刻度标签只在第一个轴（技术能力）上显示</li>
                <li>其他轴不显示刻度标签，保持图表整洁</li>
                <li>Legend显示在图表上方，支持点击切换显示/隐藏</li>
            </ul>
        </div>
    </div>

    <script>
        // 初始化图表
        const chart = echarts.init(document.getElementById('radarChart'));
        
        const option = {
            title: {
                text: '员工综合能力评估',
                left: 'center',
                top: 20,
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#333'
                }
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                show: true,
                top: 60,
                left: 'center',
                orient: 'horizontal',
                itemGap: 20,
                itemWidth: 14,
                itemHeight: 14,
                textStyle: {
                    fontSize: 12,
                    color: '#666'
                }
            },
            radar: {
                indicator: [
                    { name: '技术能力', max: 100 },
                    { name: '沟通能力', max: 100 },
                    { name: '学习能力', max: 100 },
                    { name: '创新能力', max: 100 },
                    { name: '团队协作', max: 100 },
                    { name: '执行力', max: 100 }
                ],
                radius: '60%',
                center: ['50%', '60%'],
                splitNumber: 5, // 分成5段，显示6条刻度线（包括0）
                axisName: {
                    fontSize: 12,
                    color: '#666',
                    fontWeight: 'bold'
                },
                // 显示刻度线
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#ddd',
                        width: 1,
                        type: 'solid'
                    }
                },
                // 显示刻度标签，只在第一个轴上显示
                splitLabel: {
                    show: true,
                    distance: 10,
                    textStyle: {
                        fontSize: 11,
                        color: '#999',
                        fontWeight: 'bold'
                    },
                    // 自定义格式化函数，只在第一个轴显示标签
                    formatter: function(value, index) {
                        // 只在第一个轴（index为0）显示刻度标签
                        if (index === 0) {
                            return value;
                        }
                        return '';
                    }
                },
                // 不显示背景区域
                splitArea: {
                    show: false
                },
                // 轴线配置
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#ccc',
                        width: 1
                    }
                },
                // 刻度配置
                axisTick: {
                    show: true,
                    length: 4,
                    lineStyle: {
                        color: '#ccc',
                        width: 1
                    }
                }
            },
            series: [{
                type: 'radar',
                data: [
                    {
                        value: [85, 75, 90, 80, 85, 88],
                        name: '张三',
                        itemStyle: { color: '#5470c6' },
                        lineStyle: { color: '#5470c6', width: 2 },
                        areaStyle: { color: '#5470c6', opacity: 0.1 }
                    },
                    {
                        value: [78, 88, 85, 92, 80, 85],
                        name: '李四',
                        itemStyle: { color: '#91cc75' },
                        lineStyle: { color: '#91cc75', width: 2 },
                        areaStyle: { color: '#91cc75', opacity: 0.1 }
                    },
                    {
                        value: [90, 70, 88, 85, 92, 90],
                        name: '王五',
                        itemStyle: { color: '#fac858' },
                        lineStyle: { color: '#fac858', width: 2 },
                        areaStyle: { color: '#fac858', opacity: 0.1 }
                    },
                    {
                        value: [88, 82, 75, 88, 90, 85],
                        name: '赵六',
                        itemStyle: { color: '#ee6666' },
                        lineStyle: { color: '#ee6666', width: 2 },
                        areaStyle: { color: '#ee6666', opacity: 0.1 }
                    },
                    {
                        value: [75, 90, 82, 78, 85, 88],
                        name: '钱七',
                        itemStyle: { color: '#73c0de' },
                        lineStyle: { color: '#73c0de', width: 2 },
                        areaStyle: { color: '#73c0de', opacity: 0.1 }
                    }
                ]
            }]
        };
        
        chart.setOption(option);
        
        // 响应式
        window.addEventListener('resize', function() {
            chart.resize();
        });
    </script>
</body>
</html>
