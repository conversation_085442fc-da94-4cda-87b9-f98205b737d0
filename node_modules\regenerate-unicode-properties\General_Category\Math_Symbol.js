const set = require('regenerate')(0x2B, 0x7C, 0x7E, 0xAC, 0xB1, 0xD7, 0xF7, 0x3F6, 0x2044, 0x2052, 0x2118, 0x214B, 0x21A0, 0x21A3, 0x21A6, 0x21AE, 0x21D2, 0x21D4, 0x237C, 0x25B7, 0x25C1, 0x266F, 0xFB29, 0xFE62, 0xFF0B, 0xFF5C, 0xFF5E, 0xFFE2, 0x1CEF0, 0x1D6C1, 0x1D6DB, 0x1D6FB, 0x1D715, 0x1D735, 0x1D74F, 0x1D76F, 0x1D789, 0x1D7A9, 0x1D7C3);
set.addRange(0x3C, 0x3E).addRange(0x606, 0x608).addRange(0x207A, 0x207C).addRange(0x208A, 0x208C).addRange(0x2140, 0x2144).addRange(0x2190, 0x2194).addRange(0x219A, 0x219B).addRange(0x21CE, 0x21CF).addRange(0x21F4, 0x22FF).addRange(0x2320, 0x2321).addRange(0x239B, 0x23B3).addRange(0x23DC, 0x23E1).addRange(0x25F8, 0x25FF).addRange(0x27C0, 0x27C4).addRange(0x27C7, 0x27E5).addRange(0x27F0, 0x27FF).addRange(0x2900, 0x2982).addRange(0x2999, 0x29D7).addRange(0x29DC, 0x29FB).addRange(0x29FE, 0x2AFF).addRange(0x2B30, 0x2B44).addRange(0x2B47, 0x2B4C).addRange(0xFE64, 0xFE66).addRange(0xFF1C, 0xFF1E).addRange(0xFFE9, 0xFFEC).addRange(0x10D8E, 0x10D8F).addRange(0x1EEF0, 0x1EEF1).addRange(0x1F8D0, 0x1F8D8);
exports.characters = set;
