<template>
  <div class="chart-panel">
    <!-- 头部区域：标题和返回按钮 -->
    <div class="panel-header">
      <h3 class="panel-title">{{ title }}</h3>
      <el-button 
        type="primary" 
        size="small" 
        icon="el-icon-back"
        @click="handleBack"
      >
        返回
      </el-button>
    </div>
    
    <!-- 筛选条件 -->
    <div class="panel-filters">
      <el-select 
        v-model="selectedFilter" 
        placeholder="请选择筛选条件"
        size="small"
        @change="handleFilterChange"
      >
        <el-option
          v-for="option in filterOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
    </div>
    
    <!-- 图表容器 -->
    <div class="chart-wrapper">
      <div
        :ref="chartId"
        :class="['chart-container', chartType === 'radar' ? 'radar-chart' : 'bar-chart']"
      >
        <!-- 暂无数据提示 -->
        <div v-if="!hasData" class="no-data-message">
          <i class="el-icon-warning-outline"></i>
          <span>暂无数据</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ChartPanel',
  props: {
    chartType: {
      type: String,
      required: true,
      validator: value => ['bar', 'radar'].includes(value)
    },
    chartData: {
      type: Object,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    filterOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      selectedFilter: '',
      legendData: [],
      chartId: `chart_${Math.random().toString(36).substring(2, 11)}`
    }
  },
  computed: {
    hasData() {
      if (!this.chartData || !this.chartData.series) return false
      return this.chartData.series.length > 0 &&
             this.chartData.series.some(item => item.data && item.data.length > 0)
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      const chartDom = this.$refs[this.chartId]
      if (!chartDom) return
      
      this.chart = echarts.init(chartDom)
      this.updateChart()
    },
    
    updateChart() {
      if (!this.chart) return

      // 如果没有数据，清空图表
      if (!this.hasData) {
        this.chart.clear()
        return
      }

      let option = {}

      if (this.chartType === 'bar') {
        option = this.getBarOption()
      } else if (this.chartType === 'radar') {
        option = this.getRadarOption()
      }

      this.chart.setOption(option, true)
    },
    
    getBarOption() {
      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']

      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%', // 为legend留出更多空间
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.categories,
          axisLabel: {
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 12
          }
        },
        series: this.chartData.series.map((item, index) => ({
          name: item.name,
          type: 'bar',
          data: item.data,
          itemStyle: {
            color: colors[index % colors.length]
          }
        })),
        legend: {
          show: true,
          top: '5%',
          left: 'center',
          orient: 'horizontal',
          itemGap: 15,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            fontSize: 12,
            color: '#666'
          },
          // 当legend项目很多时，允许换行显示
          type: 'scroll',
          pageIconColor: '#666',
          pageIconInactiveColor: '#ccc',
          pageTextStyle: {
            color: '#666',
            fontSize: 12
          }
        }
      }
    },
    
    getRadarOption() {
      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']

      return {
        tooltip: {
          trigger: 'item'
        },
        radar: {
          indicator: this.chartData.indicators,
          radius: '35%', // 稍微缩小雷达图，为legend留出空间
          center: ['50%', '75%'], // 向下移动雷达图中心，为上方legend留出空间
          splitNumber: 5, // 设置刻度分割段数
          axisName: {
            fontSize: 12,
            color: '#666'
          },
          // 显示刻度线
          splitLine: {
            show: true,
            lineStyle: {
              color: '#ddd',
              width: 1,
              type: 'solid'
            }
          },
          // 显示刻度标签，只在第一个轴上显示
          splitLabel: {
            show: true,
            distance: 8,
            textStyle: {
              fontSize: 10,
              color: '#999'
            },
            // 自定义格式化函数，只在第一个轴显示标签
            formatter: function(value, index) {
              // 只在第一个轴（index为0）显示刻度标签
              if (index === 0) {
                return value;
              }
              return '';
            }
          },
          // 显示刻度区域
          splitArea: {
            show: false // 不显示背景区域，只显示刻度线
          },
          // 轴线配置
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ccc',
              width: 1
            }
          },
          // 刻度配置
          axisTick: {
            show: true,
            length: 3,
            lineStyle: {
              color: '#ccc',
              width: 1
            }
          }
        },
        series: [{
          type: 'radar',
          data: this.chartData.series.map((item, index) => ({
            value: item.data,
            name: item.name,
            itemStyle: {
              color: colors[index % colors.length]
            },
            lineStyle: {
              color: colors[index % colors.length]
            },
            areaStyle: {
              color: colors[index % colors.length],
              opacity: 0.1
            }
          }))
        }],
        legend: {
          show: true,
          top: '5%',
          left: 'center',
          orient: 'horizontal',
          itemGap: 15,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            fontSize: 12,
            color: '#666'
          },
          // 当legend项目很多时，允许滚动显示
          type: 'scroll',
          pageIconColor: '#666',
          pageIconInactiveColor: '#ccc',
          pageTextStyle: {
            color: '#666',
            fontSize: 12
          }
        }
      }
    },

    
    handleBack() {
      this.$emit('back')
    },
    
    handleFilterChange(value) {
      this.$emit('filter-change', value)
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style scoped>
.chart-panel {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.panel-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.panel-filters {
  margin-bottom: 20px;
}

.chart-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.bar-chart {
  height: 350px;
}

.radar-chart {
  height: 320px;
}

.no-data-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  font-size: 14px;
  z-index: 10;
}

.no-data-message i {
  font-size: 48px;
  margin-bottom: 10px;
  color: #ddd;
}

.no-data-message span {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-panel {
    padding: 15px;
  }

  .panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .bar-chart,
  .radar-chart {
    height: 280px;
  }

  .no-data-message i {
    font-size: 36px;
  }

  .no-data-message span {
    font-size: 14px;
  }
}
</style>
