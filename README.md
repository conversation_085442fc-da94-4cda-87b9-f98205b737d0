# Vue2 + ECharts 三图表展示

这是一个基于Vue2和ECharts的数据可视化仪表板项目，展示了三个图表（两个柱形图和一个雷达图）在一行中的布局。

## 功能特点

- **三图表布局**：两个柱形图 + 一个雷达图，在一行中展示
- **完整的UI组件**：每个图表都包含标题、返回按钮和筛选条件
- **智能Legend布局**：
  - Legend显示在图表内部上方，不会覆盖雷达图内容
  - 支持多个Legend项目的滚动显示
  - 雷达图自动调整位置和大小为Legend留出空间
- **数据状态处理**：图表无数据时显示"暂无数据"提示
- **响应式设计**：支持不同屏幕尺寸的自适应布局
- **交互功能**：
  - ECharts内置Legend交互（显示/隐藏数据系列）
  - 筛选条件下拉选择
  - 返回按钮功能
  - 图表悬停提示

## 项目结构

```
├── index.html              # 单文件版本（推荐用于快速预览）
├── src/
│   ├── main.js             # Vue应用入口
│   ├── App.vue             # 主应用组件
│   └── components/
│       └── ChartPanel.vue  # 图表面板组件
├── public/
│   └── index.html          # HTML模板
├── package.json            # 项目依赖
├── webpack.config.js       # Webpack配置
└── README.md              # 项目说明
```

## 快速开始

### 方法一：直接打开HTML文件（推荐）

直接在浏览器中打开 `index.html` 文件即可查看效果。这个文件包含了完整的功能，使用CDN引入依赖。

### 方法二：使用开发环境

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 在浏览器中访问 `http://localhost:8080`

## 图表说明

### 1. 销售数据统计（柱形图）
- 展示产品A、B、C在6个月的销售数据
- 支持按产品筛选
- Legend显示在图表上方，支持多项目滚动

### 2. 用户增长趋势（柱形图）
- 展示一周内新用户和活跃用户数据
- 支持按时间周期筛选
- Legend显示在图表上方

### 3. 综合能力评估（雷达图）
- 展示员工在6个维度的能力评估（支持5个员工数据）
- 支持按部门筛选，包含"暂无数据"演示
- Legend显示在图表上方，雷达图自动调整位置避免覆盖
- 无数据时显示"暂无数据"提示

## 技术栈

- **Vue.js 2.6.14**：前端框架
- **ECharts 5.4.3**：图表库
- **Element UI 2.15.13**：UI组件库
- **Webpack 5**：构建工具

## 自定义配置

### 修改图表数据

在 `index.html` 或 `src/App.vue` 中修改对应的数据对象：

```javascript
// 柱形图数据结构
barData1: {
    categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
    series: [
        { name: '产品A', data: [120, 200, 150, 80, 70, 110] },
        { name: '产品B', data: [80, 120, 100, 140, 90, 130] }
    ]
}

// 雷达图数据结构
radarData: {
    indicators: [
        { name: '技术能力', max: 100 },
        { name: '沟通能力', max: 100 }
    ],
    series: [
        { name: '张三', data: [85, 75] },
        { name: '李四', data: [78, 88] }
    ]
}
```

### 修改筛选选项

```javascript
filterOptions1: [
    { label: '全部', value: 'all' },
    { label: '产品A', value: 'productA' }
]
```

## 响应式设计

- **桌面端**（>1200px）：三个图表在一行显示
- **平板端**（768px-1200px）：图表垂直排列
- **移动端**（<768px）：优化的移动端布局

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

MIT License
