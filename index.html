<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue2 + ECharts 三图表展示</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/echarts@5.4.3/dist/echarts.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.13/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/element-ui@2.15.13/lib/index.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Avenir', Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            color: #2c3e50;
            background-color: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .main-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 28px;
            font-weight: bold;
        }

        .charts-row {
            display: flex;
            gap: 20px;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .chart-container {
            flex: 1;
            min-width: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .chart-panel {
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .panel-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .panel-filters {
            margin-bottom: 20px;
        }

        .chart-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .chart-content {
            flex: 1;
            min-height: 300px;
            position: relative;
        }

        .bar-chart {
            height: 350px;
        }

        .radar-chart {
            height: 320px;
        }

        .no-data-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
            font-size: 14px;
            z-index: 10;
        }

        .no-data-message i {
            font-size: 48px;
            margin-bottom: 10px;
            color: #ddd;
        }

        .no-data-message span {
            font-size: 16px;
        }

        @media (max-width: 1200px) {
            .charts-row {
                flex-direction: column;
            }
            
            .chart-container {
                min-width: auto;
                margin-bottom: 20px;
            }
        }

        @media (max-width: 768px) {
            .chart-panel {
                padding: 15px;
            }
            
            .panel-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .bar-chart,
            .radar-chart {
                height: 280px;
            }

            .no-data-message i {
                font-size: 36px;
            }

            .no-data-message span {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="dashboard-container">
            <h1 class="main-title">数据可视化仪表板</h1>
            <div class="charts-row">
                <!-- 第一个柱形图 -->
                <div class="chart-container">
                    <chart-panel
                        :chart-type="'bar'"
                        :chart-data="barData1"
                        :title="'销售数据统计'"
                        :filter-options="filterOptions1"
                        @back="handleBack"
                        @filter-change="handleFilter1Change"
                    ></chart-panel>
                </div>
                
                <!-- 第二个柱形图 -->
                <div class="chart-container">
                    <chart-panel
                        :chart-type="'bar'"
                        :chart-data="barData2"
                        :title="'用户增长趋势'"
                        :filter-options="filterOptions2"
                        @back="handleBack"
                        @filter-change="handleFilter2Change"
                    ></chart-panel>
                </div>
                
                <!-- 雷达图 -->
                <div class="chart-container">
                    <chart-panel
                        :chart-type="'radar'"
                        :chart-data="currentRadarData"
                        :title="'综合能力评估'"
                        :filter-options="filterOptions3"
                        @back="handleBack"
                        @filter-change="handleFilter3Change"
                    ></chart-panel>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 图表面板组件
        Vue.component('chart-panel', {
            props: {
                chartType: {
                    type: String,
                    required: true,
                    validator: value => ['bar', 'radar'].includes(value)
                },
                chartData: {
                    type: Object,
                    required: true
                },
                title: {
                    type: String,
                    required: true
                },
                filterOptions: {
                    type: Array,
                    default: () => []
                }
            },
            data() {
                return {
                    chart: null,
                    selectedFilter: '',
                    chartId: `chart_${Math.random().toString(36).substring(2, 11)}`
                }
            },
            computed: {
                hasData() {
                    if (!this.chartData || !this.chartData.series) return false
                    return this.chartData.series.length > 0 &&
                           this.chartData.series.some(item => item.data && item.data.length > 0)
                }
            },
            template: `
                <div class="chart-panel">
                    <!-- 头部区域：标题和返回按钮 -->
                    <div class="panel-header">
                        <h3 class="panel-title">{{ title }}</h3>
                        <el-button
                            type="primary"
                            size="small"
                            icon="el-icon-back"
                            @click="handleBack"
                        >
                            返回
                        </el-button>
                    </div>

                    <!-- 筛选条件 -->
                    <div class="panel-filters">
                        <el-select
                            v-model="selectedFilter"
                            placeholder="请选择筛选条件"
                            size="small"
                            @change="handleFilterChange"
                        >
                            <el-option
                                v-for="option in filterOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </div>

                    <!-- 图表容器 -->
                    <div class="chart-wrapper">
                        <div
                            :ref="chartId"
                            :class="['chart-content', chartType === 'radar' ? 'radar-chart' : 'bar-chart']"
                        >
                            <!-- 暂无数据提示 -->
                            <div v-if="!hasData" class="no-data-message">
                                <i class="el-icon-warning-outline"></i>
                                <span>暂无数据</span>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            mounted() {
                this.$nextTick(() => {
                    this.initChart()
                })

                // 监听窗口大小变化
                window.addEventListener('resize', this.handleResize)
            },
            beforeDestroy() {
                if (this.chart) {
                    this.chart.dispose()
                }
                window.removeEventListener('resize', this.handleResize)
            },
            watch: {
                chartData: {
                    handler() {
                        this.updateChart()
                    },
                    deep: true
                }
            },
            methods: {
                initChart() {
                    const chartDom = this.$refs[this.chartId]
                    if (!chartDom) return

                    this.chart = echarts.init(chartDom)
                    this.updateChart()
                },

                updateChart() {
                    if (!this.chart) return

                    // 如果没有数据，清空图表
                    if (!this.hasData) {
                        this.chart.clear()
                        return
                    }

                    let option = {}

                    if (this.chartType === 'bar') {
                        option = this.getBarOption()
                    } else if (this.chartType === 'radar') {
                        option = this.getRadarOption()
                    }

                    this.chart.setOption(option, true)
                },

                getBarOption() {
                    const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']

                    return {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            top: '15%', // 为legend留出更多空间
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.chartData.categories,
                            axisLabel: {
                                fontSize: 12
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                fontSize: 12
                            }
                        },
                        series: this.chartData.series.map((item, index) => ({
                            name: item.name,
                            type: 'bar',
                            data: item.data,
                            itemStyle: {
                                color: colors[index % colors.length]
                            }
                        })),
                        legend: {
                            show: true,
                            top: '5%',
                            left: 'center',
                            orient: 'horizontal',
                            itemGap: 15,
                            itemWidth: 12,
                            itemHeight: 12,
                            textStyle: {
                                fontSize: 12,
                                color: '#666'
                            },
                            // 当legend项目很多时，允许换行显示
                            type: 'scroll',
                            pageIconColor: '#666',
                            pageIconInactiveColor: '#ccc',
                            pageTextStyle: {
                                color: '#666',
                                fontSize: 12
                            }
                        }
                    }
                },

                getRadarOption() {
                    const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']

                    return {
                        tooltip: {
                            trigger: 'item'
                        },
                        radar: {
                            indicator: this.chartData.indicators,
                            radius: '35%', // 稍微缩小雷达图，为legend留出空间
                            center: ['50%', '75%'], // 向下移动雷达图中心，为上方legend留出空间
                            splitNumber: 5, // 设置刻度分割段数
                            axisName: {
                                fontSize: 12,
                                color: '#666'
                            },
                            // 显示刻度线
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#ddd',
                                    width: 1,
                                    type: 'solid'
                                }
                            },
                            // 显示刻度标签，只在第一个轴上显示
                            splitLabel: {
                                show: true,
                                distance: 8,
                                textStyle: {
                                    fontSize: 10,
                                    color: '#999'
                                },
                                // 自定义格式化函数，只在第一个轴显示标签
                                formatter: function(value, index) {
                                    // 只在第一个轴（index为0）显示刻度标签
                                    if (index === 0) {
                                        return value;
                                    }
                                    return '';
                                }
                            },
                            // 显示刻度区域
                            splitArea: {
                                show: false // 不显示背景区域，只显示刻度线
                            },
                            // 轴线配置
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: '#ccc',
                                    width: 1
                                }
                            },
                            // 刻度配置
                            axisTick: {
                                show: true,
                                length: 3,
                                lineStyle: {
                                    color: '#ccc',
                                    width: 1
                                }
                            }
                        },
                        series: [{
                            type: 'radar',
                            data: this.chartData.series.map((item, index) => ({
                                value: item.data,
                                name: item.name,
                                itemStyle: {
                                    color: colors[index % colors.length]
                                },
                                lineStyle: {
                                    color: colors[index % colors.length]
                                },
                                areaStyle: {
                                    color: colors[index % colors.length],
                                    opacity: 0.1
                                }
                            }))
                        }],
                        legend: {
                            show: true,
                            top: '5%',
                            left: 'center',
                            orient: 'horizontal',
                            itemGap: 15,
                            itemWidth: 12,
                            itemHeight: 12,
                            textStyle: {
                                fontSize: 12,
                                color: '#666'
                            },
                            // 当legend项目很多时，允许滚动显示
                            type: 'scroll',
                            pageIconColor: '#666',
                            pageIconInactiveColor: '#ccc',
                            pageTextStyle: {
                                color: '#666',
                                fontSize: 12
                            }
                        }
                    }
                },


                handleBack() {
                    this.$emit('back')
                },

                handleFilterChange(value) {
                    this.$emit('filter-change', value)
                },

                handleResize() {
                    if (this.chart) {
                        this.chart.resize()
                    }
                }
            }
        })

        // 主应用
        new Vue({
            el: '#app',
            data() {
                return {
                    // 第一个柱形图数据
                    barData1: {
                        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        series: [
                            { name: '产品A', data: [120, 200, 150, 80, 70, 110] },
                            { name: '产品B', data: [80, 120, 100, 140, 90, 130] },
                            { name: '产品C', data: [60, 80, 120, 100, 110, 95] }
                        ]
                    },
                    // 第二个柱形图数据
                    barData2: {
                        categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                        series: [
                            { name: '新用户', data: [320, 280, 350, 400, 380, 420, 390] },
                            { name: '活跃用户', data: [820, 932, 901, 934, 1290, 1330, 1320] }
                        ]
                    },
                    // 雷达图数据
                    radarData: {
                        indicators: [
                            { name: '技术能力', max: 100 },
                            { name: '沟通能力', max: 100 },
                            { name: '学习能力', max: 100 },
                            { name: '创新能力', max: 100 },
                            { name: '团队协作', max: 100 },
                            { name: '执行力', max: 100 }
                        ],
                        series: [
                            { name: '张三', data: [85, 75, 90, 80, 85, 88] },
                            { name: '李四', data: [78, 88, 85, 92, 80, 85] },
                            { name: '王五', data: [90, 70, 88, 85, 92, 90] },
                            { name: '赵六', data: [88, 82, 75, 88, 90, 85] },
                            { name: '钱七', data: [75, 90, 82, 78, 85, 88] }
                        ]
                    },
                    // 空数据示例
                    emptyRadarData: {
                        indicators: [
                            { name: '技术能力', max: 100 },
                            { name: '沟通能力', max: 100 },
                            { name: '学习能力', max: 100 },
                            { name: '创新能力', max: 100 },
                            { name: '团队协作', max: 100 },
                            { name: '执行力', max: 100 }
                        ],
                        series: []
                    },
                    showEmptyData: false,
                    // 筛选选项
                    filterOptions1: [
                        { label: '全部', value: 'all' },
                        { label: '产品A', value: 'productA' },
                        { label: '产品B', value: 'productB' },
                        { label: '产品C', value: 'productC' }
                    ],
                    filterOptions2: [
                        { label: '本周', value: 'week' },
                        { label: '本月', value: 'month' },
                        { label: '本季度', value: 'quarter' }
                    ],
                    filterOptions3: [
                        { label: '全部员工', value: 'all' },
                        { label: '技术部', value: 'tech' },
                        { label: '销售部', value: 'sales' },
                        { label: '暂无数据', value: 'empty' }
                    ]
                }
            },
            computed: {
                currentRadarData() {
                    return this.showEmptyData ? this.emptyRadarData : this.radarData
                }
                }
            },
            methods: {
                handleBack() {
                    console.log('返回按钮被点击')
                    // 这里可以添加返回逻辑
                },
                handleFilter1Change(value) {
                    console.log('第一个图表筛选变化:', value)
                    // 这里可以根据筛选条件更新数据
                },
                handleFilter2Change(value) {
                    console.log('第二个图表筛选变化:', value)
                    // 这里可以根据筛选条件更新数据
                },
                handleFilter3Change(value) {
                    console.log('雷达图筛选变化:', value)
                    // 演示暂无数据功能
                    this.showEmptyData = value === 'empty'
                }
            }
        })
    </script>
</body>
</html>
