<template>
  <div id="app">
    <div class="dashboard-container">
      <h1 class="main-title">数据可视化仪表板</h1>
      <div class="charts-row">
        <!-- 第一个柱形图 -->
        <div class="chart-container">
          <ChartPanel
            :chart-type="'bar'"
            :chart-data="barData1"
            :title="'销售数据统计'"
            :filter-options="filterOptions1"
            @back="handleBack"
            @filter-change="handleFilter1Change"
          />
        </div>
        
        <!-- 第二个柱形图 -->
        <div class="chart-container">
          <ChartPanel
            :chart-type="'bar'"
            :chart-data="barData2"
            :title="'用户增长趋势'"
            :filter-options="filterOptions2"
            @back="handleBack"
            @filter-change="handleFilter2Change"
          />
        </div>
        
        <!-- 雷达图 -->
        <div class="chart-container">
          <ChartPanel
            :chart-type="'radar'"
            :chart-data="currentRadarData"
            :title="'综合能力评估'"
            :filter-options="filterOptions3"
            @back="handleBack"
            @filter-change="handleFilter3Change"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ChartPanel from './components/ChartPanel.vue'

export default {
  name: 'App',
  components: {
    ChartPanel
  },
  data() {
    return {
      // 第一个柱形图数据
      barData1: {
        categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
        series: [
          { name: '产品A', data: [120, 200, 150, 80, 70, 110] },
          { name: '产品B', data: [80, 120, 100, 140, 90, 130] },
          { name: '产品C', data: [60, 80, 120, 100, 110, 95] }
        ]
      },
      // 第二个柱形图数据
      barData2: {
        categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        series: [
          { name: '新用户', data: [320, 280, 350, 400, 380, 420, 390] },
          { name: '活跃用户', data: [820, 932, 901, 934, 1290, 1330, 1320] }
        ]
      },
      // 雷达图数据
      radarData: {
        indicators: [
          { name: '技术能力', max: 100 },
          { name: '沟通能力', max: 100 },
          { name: '学习能力', max: 100 },
          { name: '创新能力', max: 100 },
          { name: '团队协作', max: 100 },
          { name: '执行力', max: 100 }
        ],
        series: [
          { name: '张三', data: [85, 75, 90, 80, 85, 88] },
          { name: '李四', data: [78, 88, 85, 92, 80, 85] },
          { name: '王五', data: [90, 70, 88, 85, 92, 90] },
          { name: '赵六', data: [88, 82, 75, 88, 90, 85] },
          { name: '钱七', data: [75, 90, 82, 78, 85, 88] }
        ]
      },
      // 空数据示例
      emptyRadarData: {
        indicators: [
          { name: '技术能力', max: 100 },
          { name: '沟通能力', max: 100 },
          { name: '学习能力', max: 100 },
          { name: '创新能力', max: 100 },
          { name: '团队协作', max: 100 },
          { name: '执行力', max: 100 }
        ],
        series: []
      },
      showEmptyData: false,
      // 筛选选项
      filterOptions1: [
        { label: '全部', value: 'all' },
        { label: '产品A', value: 'productA' },
        { label: '产品B', value: 'productB' },
        { label: '产品C', value: 'productC' }
      ],
      filterOptions2: [
        { label: '本周', value: 'week' },
        { label: '本月', value: 'month' },
        { label: '本季度', value: 'quarter' }
      ],
      filterOptions3: [
        { label: '全部员工', value: 'all' },
        { label: '技术部', value: 'tech' },
        { label: '销售部', value: 'sales' },
        { label: '暂无数据', value: 'empty' }
      ]
    }
  },
  computed: {
    currentRadarData() {
      return this.showEmptyData ? this.emptyRadarData : this.radarData
    }
    }
  },
  methods: {
    handleBack() {
      console.log('返回按钮被点击')
      // 这里可以添加返回逻辑
    },
    handleFilter1Change(value) {
      console.log('第一个图表筛选变化:', value)
      // 这里可以根据筛选条件更新数据
    },
    handleFilter2Change(value) {
      console.log('第二个图表筛选变化:', value)
      // 这里可以根据筛选条件更新数据
    },
    handleFilter3Change(value) {
      console.log('雷达图筛选变化:', value)
      // 演示暂无数据功能
      this.showEmptyData = value === 'empty'
    }
  }
}
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20px;
}

.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
}

.main-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 28px;
  font-weight: bold;
}

.charts-row {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  flex-wrap: wrap;
}

.chart-container {
  flex: 1;
  min-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

@media (max-width: 1200px) {
  .charts-row {
    flex-direction: column;
  }
  
  .chart-container {
    min-width: auto;
    margin-bottom: 20px;
  }
}
</style>
