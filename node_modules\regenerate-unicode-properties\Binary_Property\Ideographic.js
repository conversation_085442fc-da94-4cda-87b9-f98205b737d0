const set = require('regenerate')(0x16FE4);
set.addRange(0x3006, 0x3007).addRange(0x3021, 0x3029).addRange(0x3038, 0x303A).addRange(0x3400, 0x4DBF).addRange(0x4E00, 0x9FFF).addRange(0xF900, 0xFA6D).addRange(0xFA70, 0xFAD9).addRange(0x16FF2, 0x16FF6).addRange(0x17000, 0x18CD5).addRange(0x18CFF, 0x18D1E).addRange(0x18D80, 0x18DF2).addRange(0x1B170, 0x1B2FB).addRange(0x20000, 0x2A6DF).addRange(0x2A700, 0x2B81D).addRange(0x2B820, 0x2CEAD).addRange(0x2CEB0, 0x2EBE0).addRange(0x2EBF0, 0x2EE5D).addRange(0x2F800, 0x2FA1D).addRange(0x30000, 0x3134A).addRange(0x31350, 0x33479);
exports.characters = set;
